package cn.hanyi.ctm.dataaccess.datahub;

import cn.hanyi.ctm.dataaccess.DataAccessConnection;
import cn.hanyi.ctm.dto.DataConfigDto;
import cn.hanyi.ctm.dto.DataHubConfigDto;
import cn.hanyi.ctm.dto.DataHubDataDto;
import cn.hanyi.ctm.dto.DataHubOriginDataDto;
import cn.hanyi.ctm.entity.DataAccess;
import cn.hanyi.ctm.entity.DataAccessParams;
import cn.hanyi.ctm.properties.DataHubProperties;
import com.aliyun.datahub.client.exception.SubscriptionOffsetResetException;
import com.aliyun.datahub.client.model.Field;
import com.aliyun.datahub.client.model.RecordEntry;
import com.aliyun.datahub.client.model.TupleRecordData;
import com.aliyun.datahub.clientlibrary.config.ConsumerConfig;
import com.aliyun.datahub.clientlibrary.consumer.Consumer;
import com.jayway.jsonpath.Configuration;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.utils.JsonHelper;

import java.util.*;

/**
 * 每一条启用的数据接入项对应一个连接
 * 1 建立连接
 * 2 拉取数据
 * 3 断开连接
 */
@Slf4j
public class DataHubConnection extends DataAccessConnection<DataHubDataDto> {

    private Consumer client;
    private final DataHubProperties dataHubProperties;

    public DataHubConnection(DataAccess dataAccess, List<DataAccessParams> params, DataHubProperties dataHubProperties) {
        super(dataAccess, params);
        this.dataHubProperties = dataHubProperties;
        init();
        afterInit();
    }

    @Override
    public void init() {
        DataAccess dataAccess = getDataAccess();
        DataConfigDto dataConfigDto = dataAccess.getAccessConfiguration();
        if (dataConfigDto == null || dataConfigDto.getDataHub() == null) {
            log.error("dataHub:{} config is null", dataAccess.getName());
            return;
        }
        DataHubConfigDto dataHubConfigDto = dataConfigDto.getDataHub();
        ConsumerConfig consumerConfig = new ConsumerConfig(
                dataHubConfigDto.getEndPoint(),
                dataHubConfigDto.getAccessId(),
                dataHubConfigDto.getAccessKey()
        );
        consumerConfig.setOffsetCommitTimeoutMs(dataHubProperties.getCommitTimeOut());
        consumerConfig.setSessionTimeoutMs(dataHubProperties.getSessionTimeOut());
        consumerConfig.setAutoCommit(dataHubProperties.isAutoCommit());
        client = new Consumer(
                dataHubConfigDto.getProjectName(),
                dataHubConfigDto.getTopicName(),
                dataHubConfigDto.getSubId(),
                consumerConfig
        );

    }

    @Override
    public List<DataHubDataDto> getData(int size) {
        List<DataHubDataDto> result = new ArrayList<>();
        if (client == null) {
            return result;
        }
        try {
            for (int i = 0; i < size; i++) {
                RecordEntry record = client.read(dataHubProperties.getMaxRetry());
                if (record != null) {
                    Map<String, Object> item = new HashMap<>();
                    try {
                        TupleRecordData recordData = (TupleRecordData) record.getRecordData();
                        DataHubOriginDataDto originDataDto = DataHubOriginDataDto.builder()
                                .shardId(record.getShardId())
                                .systemTime(record.getSystemTime())
                                .sequence(record.getSequence())
                                .cursor(record.getCursor())
                                .build();
                        item.put("origin", originDataDto);
                        for (Field schema : recordData.getRecordSchema().getFields()) {
                            try {
                                Object data = recordData.getField(schema.getName());
                                if (Objects.toString(data).startsWith("{") && Objects.toString(data).endsWith("}")) {
                                    data = Configuration.defaultConfiguration().jsonProvider().parse((String) data);
                                }
                                if (data != null) {
                                    item.put(schema.getName(), data);
                                }
                            } catch (Throwable e) {
                                // 这条消息的这个字段处理失败了，跳过这个字段
                                log.warn("record data field parse error skip {}", schema.getName());
                            }
                        }
                    } catch (Throwable e) {
                        // 只要读取到了消息，中间的任何异常都不能直接抛出，导致此消息无法 ack
                        log.warn("read message throw exception, key:{} ", record.getKey().toString(), e);
                    }
                    result.add(new DataHubDataDto(record.getKey(), JsonHelper.toJson(item)));
                } else {
                    return result;
                }
            }
        } catch (SubscriptionOffsetResetException e) {
            client.close();
            init();
        } catch (Throwable e) {
            log.error("dataHub:{} get data error", getDataAccess().getName(), e);
            // throw e; 不抛出异常了， 如果这个时候已经拉取到了消息，抛出异常，会导致消息丢失，无法 ack
        }
        return result;
    }

    @Override
    public void ack(DataHubDataDto data) {
        if (client != null) {
            data.getRecordKey().ack();
        }
    }

    @Override
    public void release() {
        if (client != null) {
            client.close();
        }
    }
}
