data-access:
  pull-interval: 5000
  pull-size: 10
  consumer-threads: 10
  consumer-wait-seconds: 90
  datahub:
    enabled: ${DATA_ACCESS_DATAHUB_ENABLED:false}
    max-connection: 50
    client-time-out: 30000
    commit-time-out: 30000
    max-retry: 1
    auto-commit: false
    enable-mock-connection: false
    mock-connection-queue: data-access:data-hub-mock-data:%d
  scheduling:
    enabled: ${DATA_ACCESS_SCHEDULING_ENABLED:false}
    cron: ${DATA_ACCESS_SCHEDULING_CRON:*/5 * * * * *}