package cn.hanyi.ctm.entity;

import cn.hanyi.ctm.constant.TemplateCreateType;
import cn.hanyi.ctm.constant.connector.ConnectorProviderType;
import cn.hanyi.ctm.constant.connector.ConnectorType;
import cn.hanyi.ctm.dto.connector.ThirdPartyTemplateCreateDto;
import cn.hanyi.ctm.dto.connector.ThirdPartyTemplateDto;
import cn.hanyi.ctm.dto.connector.ThirdPartyTemplateUpdateDto;
import cn.hanyi.ctm.dto.ext.ThirdPartyTemplateExtDto;
import cn.hanyi.ctm.properties.ConnectorInitProperties;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import me.chanjar.weixin.mp.bean.template.WxMpTemplate;
import org.befun.core.converter.MapListConverter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.rest.view.ResourceViews;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Entity
@Getter
@Setter
@Table(name = "thirdparty_template")
@NoArgsConstructor
@EntityScopeStrategy
@DtoClass(includeAllFields = true, superClass = ThirdPartyTemplateExtDto.class)
public class ThirdPartyTemplate extends EnterpriseEntity {

    @Deprecated
    @ManyToOne
    @JoinColumn(name = "connector_id")
    @JsonView(ResourceViews.Detail.class)
    @NotFound(action = NotFoundAction.IGNORE)
    private Connector connector;

    @Column(name = "thirdparty_auth_id")
    private Long thirdpartyAuthId;

    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class)
    private String name;

    @JsonView(ResourceViews.Basic.class)
    private String description;

    @JsonView(ResourceViews.Basic.class)
    private String example;

    @Column(name = "open_id")
    @JsonView(ResourceViews.Detail.class)
    private String openId;

    @Column(name = "signature_id")
    @JsonView(ResourceViews.Detail.class)
    private String signatureId;

    @Convert(converter = MapListConverter.class)
    @JsonView(ResourceViews.Basic.class)
    private List<Map<String, Object>> parameters = new ArrayList<>();

    @Column(name = "provider_type")
    @Enumerated(EnumType.ORDINAL)
    @JsonView(ResourceViews.Basic.class)
    private ConnectorProviderType providerType;

    @Column(name = "connector_type")
    @Enumerated(EnumType.ORDINAL)
    @JsonView(ResourceViews.Basic.class)
    private ConnectorType connectorType;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "ORGANIZATION： 企业创建的模板， 其他： 系统模板")
    @Column(name = "create_type")
    @Enumerated
    private TemplateCreateType createType = TemplateCreateType.ORGANIZATION;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "status")
    @Schema(description = "模板状态：0 待审核 1 审核通过")
    private Integer status = 1;

    @Column(name = "is_delete")
    private Boolean isDelete = false;

    @Column(name = "create_user_id")
    private Long createUserId;

    @Column(name = "update_user_id")
    private Long updateUserId;

    @Column(name = "real_signature")
    @JsonView(ResourceViews.Detail.class)
    private String realSignature;

    public ThirdPartyTemplate(ThirdPartyTemplateDto dto, Connector connector) {
        this.name = dto.getTitle();
        this.example = dto.getExample();
        this.openId = dto.getOpenId();
        this.parameters = dto.getParams();
        this.connector = connector;
        this.providerType = connector.getProviderType();
        this.connectorType = connector.getType();
        this.orgId = connector.getOrgId();
        this.createUserId = TenantContext.getCurrentUserId();
        this.updateUserId = TenantContext.getCurrentUserId();
    }

    public ThirdPartyTemplate(Long orgId, Long thirdpartyAuthId, WxMpTemplate template, List<Map<String, Object>> parameters) {
        this.orgId = orgId;
        this.thirdpartyAuthId = thirdpartyAuthId;
        this.name = template.getTitle();
        this.example = template.getExample();
        this.openId = template.getTemplateId();
        this.parameters = parameters;
        this.providerType = ConnectorProviderType.WECHATOPEN;
        this.connectorType = ConnectorType.PLATFORM;
        this.createUserId = TenantContext.getCurrentUserId();
        this.updateUserId = TenantContext.getCurrentUserId();
    }

    public static ThirdPartyTemplate mapToAdd(Long orgId, ThirdPartyTemplateCreateDto dto, List<Map<String, Object>> parameters) {
        ThirdPartyTemplate entity = new ThirdPartyTemplate();
        entity.setOrgId(orgId);
        entity.setName(dto.getName());
        entity.setDescription(dto.getSubject());
        entity.setExample(dto.getContent());
        entity.setParameters(parameters);
        entity.setProviderType(dto.getConnectorType().getDefaultProviderType());
        entity.setConnectorType(dto.getConnectorType());
        entity.setStatus(1);
        entity.setCreateUserId(TenantContext.getCurrentUserId());
        entity.setUpdateUserId(TenantContext.getCurrentUserId());
        return entity;
    }

    public static ThirdPartyTemplate mapToEdit(ThirdPartyTemplate entity, ThirdPartyTemplateUpdateDto dto, List<Map<String, Object>> parameters) {
        entity.setName(dto.getName());
        entity.setDescription(dto.getSubject());
        entity.setExample(dto.getContent());
        entity.setParameters(parameters);
        entity.setUpdateUserId(TenantContext.getCurrentUserId());
        return entity;
    }

    public static ThirdPartyTemplate mapToAdd(Long orgId, ConnectorInitProperties.InitTemplate init) {
        ThirdPartyTemplate entity = new ThirdPartyTemplate();
        entity.setName(init.getName());
        entity.setOrgId(orgId);
        entity.setCreateType(init.getCreateType());
        entity.setProviderType(init.getConnectorType().getDefaultProviderType());
        entity.setConnectorType(init.getConnectorType());
        entity.setStatus(1);
        return entity;
    }

    public TemplateCreateType getCreateType() {
        return createType == null ? TemplateCreateType.ORGANIZATION : createType;
    }
}
