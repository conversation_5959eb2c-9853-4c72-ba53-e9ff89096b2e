package cn.hanyi.ctm.sendmanage;

import cn.hanyi.ctm.entity.Customer;
import cn.hanyi.ctm.entity.SendManage;
import cn.hanyi.ctm.entity.SendManageRecord;
import cn.hanyi.ctm.repository.SendManageRecordRepository;
import cn.hanyi.ctm.repository.SendManageRepository;
import cn.hanyi.ctm.service.CustomerService;
import cn.hanyi.survey.core.projection.SimpleSurvey;
import cn.hanyi.survey.core.repository.SurveyRepository;
import org.befun.extension.entity.Link;
import org.befun.extension.service.LinkService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SendMangeHelperLinkPersistenceTest {

    @Mock
    private SendManageRepository sendManageRepository;
    
    @Mock
    private SendManageRecordRepository sendManageRecordRepository;
    
    @Mock
    private SurveyRepository surveyRepository;
    
    @Mock
    private LinkService linkService;
    
    @Mock
    private CustomerService customerService;
    
    @Mock
    private StringRedisTemplate stringRedisTemplate;

    @InjectMocks
    private TestSendMangeHelper sendMangeHelper;

    private SendManage sendManage;
    private Customer customer;
    private SimpleSurvey survey;
    private Link mockLink;

    // Test implementation of SendMangeHelper to access protected methods
    private static class TestSendMangeHelper extends SendMangeHelper {
        public SendManageRecord testSendManage(Long orgId, Long userId, Long sourceId, Long taskProgressId, 
                                               SendManage sendManage, Map<String, Object> params,
                                               java.util.function.Supplier<Boolean> checkFilter,
                                               java.util.function.Supplier<Customer> getCustomer) {
            return sendManage(orgId, userId, sourceId, taskProgressId, sendManage, params, checkFilter, getCustomer);
        }
    }

    @BeforeEach
    void setUp() {
        // Setup SendManage
        sendManage = new SendManage();
        sendManage.setId(1L);
        sendManage.setOrgId(100L);
        sendManage.setEnable(true);
        sendManage.setSendSids(List.of(1001L));

        // Setup Customer
        customer = new Customer();
        customer.setId(2001L);

        // Setup Survey
        survey = new SimpleSurvey();
        survey.setId(1001L);
        survey.setTitle("Test Survey");

        // Setup Mock Link
        mockLink = new Link();
        mockLink.setId(3001L);
    }

    @Test
    void testLinkCreationSuccess() {
        // Arrange
        when(surveyRepository.findSimpleById(1001L)).thenReturn(survey);
        when(linkService.createLink(eq(1001L), eq(2), any(Map.class))).thenReturn(mockLink);
        when(linkService.toShortUrl(mockLink)).thenReturn("https://short.url/abc123");
        when(linkService.toOriginUrl(mockLink)).thenReturn("https://origin.url/survey/1001");
        when(linkService.toShortCode(mockLink)).thenReturn("abc123");
        
        SendManageRecord savedRecord = new SendManageRecord();
        savedRecord.setId(4001L);
        when(sendManageRecordRepository.save(any(SendManageRecord.class))).thenReturn(savedRecord);

        // Act
        SendManageRecord result = sendMangeHelper.testSendManage(
            100L, 200L, 300L, null, sendManage, new HashMap<>(),
            () -> true,
            () -> customer
        );

        // Assert
        assertNotNull(result);
        assertEquals(3001L, result.getLinkId());
        assertEquals("https://short.url/abc123", result.getSendUrl());
        
        // Verify Link creation was called
        verify(linkService).createLink(eq(1001L), eq(2), any(Map.class));
        verify(linkService, atLeastOnce()).toShortUrl(mockLink);
    }

    @Test
    void testLinkCreationFailure_NullLink() {
        // Arrange
        when(surveyRepository.findSimpleById(1001L)).thenReturn(survey);
        when(linkService.createLink(eq(1001L), eq(2), any(Map.class))).thenReturn(null);
        
        SendManageRecord savedRecord = new SendManageRecord();
        savedRecord.setId(4001L);
        when(sendManageRecordRepository.save(any(SendManageRecord.class))).thenReturn(savedRecord);

        // Act
        SendManageRecord result = sendMangeHelper.testSendManage(
            100L, 200L, 300L, null, sendManage, new HashMap<>(),
            () -> true,
            () -> customer
        );

        // Assert
        assertNotNull(result);
        assertNull(result.getLinkId());
        assertNull(result.getSendUrl());
        
        // Verify the record was saved with NO_CHANNEL status
        verify(sendManageRecordRepository, atLeast(2)).save(any(SendManageRecord.class));
    }

    @Test
    void testLinkCreationFailure_NullLinkId() {
        // Arrange
        Link linkWithoutId = new Link();
        // linkWithoutId.setId(null); // ID is null
        
        when(surveyRepository.findSimpleById(1001L)).thenReturn(survey);
        when(linkService.createLink(eq(1001L), eq(2), any(Map.class))).thenReturn(linkWithoutId);
        
        SendManageRecord savedRecord = new SendManageRecord();
        savedRecord.setId(4001L);
        when(sendManageRecordRepository.save(any(SendManageRecord.class))).thenReturn(savedRecord);

        // Act
        SendManageRecord result = sendMangeHelper.testSendManage(
            100L, 200L, 300L, null, sendManage, new HashMap<>(),
            () -> true,
            () -> customer
        );

        // Assert
        assertNotNull(result);
        assertNull(result.getLinkId());
        assertNull(result.getSendUrl());
    }

    @Test
    void testLinkVerificationFailure_EmptyShortUrl() {
        // Arrange
        when(surveyRepository.findSimpleById(1001L)).thenReturn(survey);
        when(linkService.createLink(eq(1001L), eq(2), any(Map.class))).thenReturn(mockLink);
        when(linkService.toShortUrl(mockLink)).thenReturn(""); // Empty short URL
        
        SendManageRecord savedRecord = new SendManageRecord();
        savedRecord.setId(4001L);
        when(sendManageRecordRepository.save(any(SendManageRecord.class))).thenReturn(savedRecord);

        // Act
        SendManageRecord result = sendMangeHelper.testSendManage(
            100L, 200L, 300L, null, sendManage, new HashMap<>(),
            () -> true,
            () -> customer
        );

        // Assert
        assertNotNull(result);
        assertNull(result.getLinkId());
        assertNull(result.getSendUrl());
    }

    @Test
    void testLinkVerificationWithRetry() {
        // Arrange
        when(surveyRepository.findSimpleById(1001L)).thenReturn(survey);
        when(linkService.createLink(eq(1001L), eq(2), any(Map.class))).thenReturn(mockLink);
        
        // First two calls fail, third succeeds (simulating eventual consistency)
        when(linkService.toShortUrl(mockLink))
            .thenReturn("")
            .thenReturn("")
            .thenReturn("https://short.url/abc123");
        when(linkService.toOriginUrl(mockLink)).thenReturn("https://origin.url/survey/1001");
        when(linkService.toShortCode(mockLink)).thenReturn("abc123");
        
        SendManageRecord savedRecord = new SendManageRecord();
        savedRecord.setId(4001L);
        when(sendManageRecordRepository.save(any(SendManageRecord.class))).thenReturn(savedRecord);

        // Act
        SendManageRecord result = sendMangeHelper.testSendManage(
            100L, 200L, 300L, null, sendManage, new HashMap<>(),
            () -> true,
            () -> customer
        );

        // Assert
        assertNotNull(result);
        assertEquals(3001L, result.getLinkId());
        assertEquals("https://short.url/abc123", result.getSendUrl());
        
        // Verify retry mechanism was used
        verify(linkService, times(3)).toShortUrl(mockLink);
    }
}
