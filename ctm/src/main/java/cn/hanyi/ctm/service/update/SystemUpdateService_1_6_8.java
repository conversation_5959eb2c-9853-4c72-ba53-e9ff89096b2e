package cn.hanyi.ctm.service.update;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.constant.AppVersion;
import org.befun.auth.entity.*;
import org.befun.auth.repository.OrganizationRepository;
import org.befun.auth.repository.OrganizationWalletRepository;
import org.befun.auth.service.DepartmentService;
import org.befun.auth.service.RoleService;
import org.befun.auth.service.UserRoleService;
import org.befun.auth.service.UserService;
import org.befun.auth.utils.PasswordHelper;
import org.befun.core.exception.BadRequestException;
import org.befun.extension.service.AbstractSystemUpdateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.Map;
import java.util.function.Consumer;

@Slf4j
@Service
public class SystemUpdateService_1_6_8 extends AbstractSystemUpdateService {

    @Autowired
    private OrganizationRepository organizationRepository;
    @Autowired
    private OrganizationWalletRepository organizationWalletRepository;
    @Autowired
    private DepartmentService departmentService;
    @Autowired
    private UserService userService;
    @Autowired
    private RoleService roleService;
    @Autowired
    private UserRoleService userRoleService;

    @Override
    public String getSecret() {
        return "bf4d4a6a-5e2b-4b70-9e27-19d4bb499b81";
    }

    @Override
    public Map<String, Consumer<Map<String, String>>> getUpdateTasks() {
        return Map.of(
                "initOrg", getInitOrgTask()
        );
    }

    public Consumer<Map<String, String>> getInitOrgTask() {
        return data -> {
            if (data == null || data.isEmpty()) {
                throw new BadRequestException("part: initOrg 需要 orgName, mobile, email, password, ");
            }
            String orgName = data.get("orgName");
            String password = data.get("password");
            String mobile = data.get("mobile");
            String email = data.get("email");
            String orgCode = data.get("orgCode");
            if (StringUtils.isEmpty(orgName)
                    || StringUtils.isEmpty(mobile)
                    || StringUtils.isEmpty(email)
                    || StringUtils.isEmpty(password)
                    || StringUtils.isEmpty(orgCode)
            ) {
                throw new BadRequestException("part: initOrg 需要 orgName,orgCode, mobile, email, password");
            }

            if (userService.existsByMobile(mobile)) {
                throw new BadRequestException("mobile 已存在");
            }
            if (userService.existsByEmail(email)) {
                throw new BadRequestException("email 已存在");
            }

            Date s = new Date();
            Date e = Date.from(LocalDateTime.now().plusYears(50).atZone(ZoneId.systemDefault()).toInstant());
            int created = (int) (System.currentTimeMillis() / 1000);

            // org wallet
            Organization org = new Organization(orgName, 9L, 0L, Integer.MAX_VALUE, 0, orgCode, 0,
                    s, e, created + "", created + "",
                    "{\"surveyplus_version\":\"base\",\"cem_version\":\"profession\"}",null,
                    null, null, null, null, null, null);
            organizationRepository.save(org);
            OrganizationWallet wallet = new OrganizationWallet();
            wallet.setOrgId(org.getId());
            wallet.setMoney(0);
            wallet.setSms(Integer.MAX_VALUE);
            wallet.setOrgId(org.getId());
            organizationWalletRepository.save(wallet);

            // department
            Department rootDepartment = departmentService.initDepartment(org);

            // user
            User user = new User();
            user.setPassword(PasswordHelper.encrypt(password));
            user.setPasswordStrength(PasswordHelper.passwordStrength(password));
            user.setMobile(mobile);
            user.setEmail(email);
            user.setIsAdmin(true);
            user.setTruename("超级管理员");
            user.setNickname("超级管理员");
            user.setAvailableSystems("{\"login_surveyplus\":1,\"login_cem\":1}");
            user.setStatus(1);
            user.setIsFinishedGuide("N");
            user.setCreated(created);
            user.setUpdated(created);
            user.setIsDelete(0);
            user.setOrgId(org.getId());
            user.appendDepartmentId(rootDepartment.getId());
            userService.save(user);

            // role permission userRole
            Role superAdmin = roleService.addSuperAdmin(org.getId(), AppVersion.PROFESSION);

            userRoleService.addUserRole(user, superAdmin);

            roleService.addMemberRole(org.getId());

            // update org owner
            org.setOwnerId(user.getId());
            organizationRepository.save(org);

        };
    }

}
