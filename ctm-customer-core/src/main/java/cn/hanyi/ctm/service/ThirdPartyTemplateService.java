package cn.hanyi.ctm.service;

import cn.hanyi.ctm.connector.impl.wechatopen.TemplateParser;
import cn.hanyi.ctm.constant.TemplateCreateType;
import cn.hanyi.ctm.constant.connector.ConnectorType;
import cn.hanyi.ctm.dto.connector.ThirdPartyTemplateCreateDto;
import cn.hanyi.ctm.dto.connector.ThirdPartyTemplateUpdateDto;
import cn.hanyi.ctm.entity.ThirdPartyTemplate;
import cn.hanyi.ctm.entity.ThirdPartyTemplateDto;
import cn.hanyi.ctm.properties.ConnectorInitProperties;
import cn.hanyi.ctm.properties.PlaceHolderProperties;
import cn.hanyi.ctm.repository.ThirdPartyTemplateRepository;
import me.chanjar.weixin.mp.bean.template.WxMpTemplate;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.constant.ThirdPartyAuthType;
import org.befun.auth.entity.ThirdPartyAuth;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.ThirdPartyAuthService;
import org.befun.auth.service.UserService;
import org.befun.auth.service.auth.AuthWechatOpenService;
import org.befun.auth.service.auth.config.WechatOpenConfig;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.dto.query.ResourceQueryCriteria;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.BaseService;
import org.befun.core.utils.JsonHelper;
import org.befun.core.utils.ListHelper;
import org.befun.extension.property.MailTemplateProperty;
import org.befun.extension.property.SmsTemplateProperty;
import org.befun.extension.service.MailService;
import org.befun.extension.service.SmsService;
import org.befun.extension.service.WeChatOpenService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.hanyi.ctm.constant.TemplateCreateType.ORGANIZATION;
import static cn.hanyi.ctm.constant.connector.ConnectorType.EMAIL;
import static cn.hanyi.ctm.constant.connector.ConnectorType.SMS;

@Service
public class ThirdPartyTemplateService extends BaseService<ThirdPartyTemplate, ThirdPartyTemplateDto, ThirdPartyTemplateRepository> {

    @Autowired
    private ThirdPartyTemplateRepository repository;
    @Autowired
    private PlaceHolderProperties placeHolderProperties;
    @Autowired
    private ConnectorInitProperties connectorInitProperties;
    @Autowired
    private ThirdPartyAuthService thirdPartyAuthService;
    @Autowired
    private AuthWechatOpenService authWechatOpenService;
    @Autowired
    private WeChatOpenService weChatOpenService;
    @Autowired
    private UserService userService;
    @Autowired(required = false)
    private SmsService smsService;
    @Autowired(required = false)
    private MailService mailService;

    @Override
    public Page<ThirdPartyTemplateDto> findAll(ResourceEntityQueryDto<ThirdPartyTemplateDto> queryDto) {
        confirmInitThirdPartyTemplate();
        if (queryDto.getQueryCriteriaList().stream().noneMatch(i -> i.getKey().startsWith("status"))) {
            queryDto.addCriteria(new ResourceQueryCriteria("status", 1));
        }
        if (queryDto.getSorts() == null || queryDto.getSorts().isUnsorted()) {
            queryDto.setSorts(Sort.by(Sort.Order.desc("createType"), Sort.Order.desc("createTime")));
        }
        return super.findAll(queryDto);
    }

    @Override
    public void afterMapToDto(List<ThirdPartyTemplate> entity, List<ThirdPartyTemplateDto> dto) {
        if (CollectionUtils.isEmpty(dto)) {
            return;
        }
        confirmTemplateInfo(dto);
        Set<Long> userIds = new HashSet<>();
        dto.forEach(i -> {
            Optional.ofNullable(i.getCreateUserId()).ifPresent(userIds::add);
            Optional.ofNullable(i.getUpdateUserId()).ifPresent(userIds::add);
        });
        Map<Long, SimpleUser> userMap = userService.getSimpleMapByIds(userIds);
        Map<ConnectorType, Boolean> enableCustomMap = new HashMap<>();
        dto.forEach(i -> {
            Boolean enableCustom = enableCustomMap.get(i.getConnectorType());
            if (enableCustom == null) {
                enableCustom = enableCustom(i.getConnectorType());
                enableCustomMap.put(i.getConnectorType(), enableCustom);
            }
            i.setCreateUser(userMap.get(i.getCreateUserId()));
            i.setModifyUser(userMap.get(i.getUpdateUserId()));
            i.setCanAudit(enableCustom && canAudit(i.getEntity()));
            i.setCanUpdate(enableCustom && canUpdate(i.getEntity()));
            i.setCanDelete(enableCustom && canDelete(i.getEntity()));
        });
    }

    private boolean canAudit(ThirdPartyTemplate entity) {
        return entity.getCreateType() == ORGANIZATION && entity.getStatus() == 0;
    }

    private boolean canUpdate(ThirdPartyTemplate entity) {
        return entity.getCreateType() == ORGANIZATION;
    }

    private boolean canDelete(ThirdPartyTemplate entity) {
        return entity.getCreateType() == ORGANIZATION;
    }

    private Map<String, MailTemplateProperty> getPlatformEmailTemplateMap() {
        if (mailService == null) {
            return new HashMap<>();
        }
        return mailService.getTemplatePropertyMap();
    }

    public MailTemplateProperty getPlatformMailTemplate(String templateName) {
        return getPlatformEmailTemplateMap().get(templateName);
    }

    private Map<String, SmsTemplateProperty> getPlatformSmsTemplateMap() {
        if (smsService == null) {
            return new HashMap<>();
        }
        return smsService.getTemplatePropertyMap();
    }

    public SmsTemplateProperty getPlatformSmsTemplate(String templateName) {
        return getPlatformSmsTemplateMap().get(templateName);
    }

    public String getSmsTemplateRealSign() {
        return smsService == null ? "" : smsService.getRealSignature();
    }

    public String getSmsTemplateRealSign(ThirdPartyTemplate template) {
        return smsService == null ? "" : StringUtils.isNotEmpty(template.getRealSignature()) ? template.getRealSignature() : smsService.getRealSignature();
    }

    private void confirmTemplateInfo(List<ThirdPartyTemplateDto> dto) {
        Set<ThirdPartyTemplate> updateList = new HashSet<>();
        SimpleUser orgUser = null;
        for (ThirdPartyTemplateDto i : dto) {
            if (i.getCreateType() != ORGANIZATION) {
                Function<String, List<Map<String, Object>>> parametersFun = s -> Optional.ofNullable(JsonHelper.toList(s, PlaceHolderProperties.PlaceHolderValue.class)).map(j -> j.stream().map(PlaceHolderProperties.PlaceHolderValue::toMap).collect(Collectors.toList())).orElse(new ArrayList<>());
                if (i.getConnectorType() == SMS) {
                    Map<TemplateCreateType, ConnectorInitProperties.InitTemplate> initTemplateMap = getInitTemplate();
                    Map<String, SmsTemplateProperty> platformSmsTemplateMap = getPlatformSmsTemplateMap();
                    SmsTemplateProperty property = platformSmsTemplateMap.get(i.getCreateType().name());
                    ConnectorInitProperties.InitTemplate initTemplate = initTemplateMap.get(i.getCreateType());
                    if (property == null || initTemplate == null) {
                        i.setStatus(0);
                    } else {
                        i.setName(initTemplate.getName());
                        i.setExample(property.getContent());
                        i.setParameters(parametersFun.apply(property.getParameters()));
                        i.setOpenId(property.getId());
                        i.setSignatureId(property.getSignature());
                    }
                } else if (i.getConnectorType() == EMAIL) {
                    Map<TemplateCreateType, ConnectorInitProperties.InitTemplate> initTemplateMap = getInitTemplate();
                    Map<String, MailTemplateProperty> platformEmailTemplateMap = getPlatformEmailTemplateMap();
                    MailTemplateProperty property = platformEmailTemplateMap.get(i.getCreateType().name());
                    ConnectorInitProperties.InitTemplate initTemplate = initTemplateMap.get(i.getCreateType());
                    if (property == null || initTemplate == null) {
                        i.setStatus(0);
                    } else {
                        i.setName(initTemplate.getName());
                        i.setDescription(property.getSubject());
                        i.setExample(property.getContent());
                        i.setParameters(new ArrayList<>());
                    }
                }
            }
            ThirdPartyTemplate entity = i.getEntity();
            if (entity.getStatus() == null) {
                entity.setStatus(1);
                i.setStatus(entity.getStatus());
                updateList.add(entity);
            }
            if (entity.getCreateType() == null) {
                entity.setCreateType(ORGANIZATION);
                i.setCreateType(entity.getCreateType());
                updateList.add(entity);
            }
            if (entity.getCreateUserId() == null) {
                if (orgUser == null) {
                    orgUser = userService.requireCurrentOrgUser();
                }
                entity.setCreateUserId(orgUser.getId());
                i.setCreateUserId(entity.getCreateUserId());
                updateList.add(entity);
            }
            if (entity.getUpdateUserId() == null) {
                if (orgUser == null) {
                    orgUser = userService.requireCurrentOrgUser();
                }
                entity.setUpdateUserId(orgUser.getId());
                i.setUpdateUserId(entity.getUpdateUserId());
                updateList.add(entity);
            }
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            repository.saveAll(updateList);
        }
    }

    public List<PlaceHolderProperties.PlaceHolderConfig> getPlaceHolders() {
        return placeHolderProperties.getConfig();
    }

    private List<Map<String, Object>> parsePlaceHolders(String content) {
        if (StringUtils.isEmpty(content)) {
            return List.of();
        }
        List<Map<String, Object>> parameters = new ArrayList<>();
        placeHolderProperties.getPlaceholders().forEach(i -> {
            if (content.contains(i.getKey())) {
                parameters.add(i.toMap());
            }
        });
        return parameters;
    }

    private boolean enableCustom(ConnectorType connectorType) {
        return connectorInitProperties.getEnableCustom().getOrDefault(connectorType, false);
    }

    private void checkEnableCustom(ConnectorType connectorType) {
        if (!enableCustom(connectorType)) {
            throw new BadRequestException("自定义消息库已被禁用");
        }
    }

    @Override
    @Transactional
    public <S extends BaseEntityDTO<ThirdPartyTemplate>> ThirdPartyTemplateDto create(S data) {
        ThirdPartyTemplateCreateDto dto = (ThirdPartyTemplateCreateDto) data;
        checkEnableCustom(dto.getConnectorType());
        if (dto.getConnectorType() == SMS || dto.getConnectorType() == EMAIL) {
            // skip
        } else {
            throw new BadRequestException("不支持的消息模板类型");
        }
        ThirdPartyTemplate entity = ThirdPartyTemplate.mapToAdd(TenantContext.requireCurrentTenant(), dto, parsePlaceHolders(dto.getContent()));
        repository.save(entity);
        return mapToDto(entity);
    }

    public void addPlatformSmsTemplate(Long orgId, String name, String content, String templateId, String signId) {
        ThirdPartyTemplateCreateDto createDto = new ThirdPartyTemplateCreateDto();
        createDto.setName(name);
        createDto.setContent(content);
        createDto.setConnectorType(SMS);
        ThirdPartyTemplate entity = ThirdPartyTemplate.mapToAdd(orgId, createDto, new ArrayList<>());
        entity.setOpenId(templateId);
        entity.setSignatureId(signId);
        repository.save(entity);
    }

    @Override
    @Transactional
    public <S extends BaseEntityDTO<ThirdPartyTemplate>> ThirdPartyTemplateDto updateOne(long id, S change) {
        ThirdPartyTemplateUpdateDto dto = (ThirdPartyTemplateUpdateDto) change;
        ThirdPartyTemplate entity = require(id);
        checkEnableCustom(entity.getConnectorType());
        if (canUpdate(entity)) {
            ThirdPartyTemplate.mapToEdit(entity, dto, parsePlaceHolders(dto.getContent()));
            repository.save(entity);
            return mapToDto(entity);
        } else {
            throw new BadRequestException("无权限编辑");
        }
    }

    @Override
    @Transactional
    public Boolean deleteOne(long id) {
        ThirdPartyTemplate entity = require(id);
        checkEnableCustom(entity.getConnectorType());
        if (canDelete(entity)) {
            repository.delete(entity);
            return true;
        } else {
            throw new BadRequestException("无权限删除");
        }
    }

    @Transactional
    public boolean audit(Long id) {
        ThirdPartyTemplate entity = require(id);
        if (canAudit(entity)) {
            entity.setStatus(1);
            entity.setUpdateUserId(TenantContext.getCurrentUserId());
            repository.save(entity);
            return true;
        } else {
            throw new BadRequestException("无权限启用");
        }
    }

    private Map<TemplateCreateType, ConnectorInitProperties.InitTemplate> getInitTemplate() {
        return connectorInitProperties.getTemplates().stream().collect(Collectors.toMap(ConnectorInitProperties.InitTemplate::getCreateType, Function.identity(), (o1, o2) -> o2));
    }

    public void confirmInitThirdPartyTemplate() {
        long orgId = TenantContext.requireCurrentTenant();
        Set<TemplateCreateType> oldTypes = new HashSet<>();
        List<ThirdPartyTemplate> oldList = repository.findByCreateTypeNot(ORGANIZATION);
        if (CollectionUtils.isNotEmpty(oldList)) {
            oldList.forEach(i -> oldTypes.add(i.getCreateType()));
        }
        List<ConnectorInitProperties.InitTemplate> newList = connectorInitProperties.getTemplates();
        List<ThirdPartyTemplate> add = new ArrayList<>();
        newList.forEach(i -> {
            if (!oldTypes.contains(i.getCreateType())) {
                if (i.getConnectorType() == SMS) {
                    add.add(ThirdPartyTemplate.mapToAdd(orgId, i));
                } else if (i.getConnectorType() == EMAIL) {
                    add.add(ThirdPartyTemplate.mapToAdd(orgId, i));
                }
            }
        });

        if (CollectionUtils.isNotEmpty(add)) {
            Optional.ofNullable(userService.requireCurrentOrgUser()).ifPresent(orgUser -> {
                add.forEach(i -> {
                    i.setCreateUserId(orgUser.getId());
                    i.setUpdateUserId(orgUser.getId());
                });
            });
            repository.saveAll(add);
        }
    }

    public List<ThirdPartyTemplate> syncTemplate(Long orgId, Long configId) {
        ThirdPartyAuth entity = thirdPartyAuthService.get(configId);
        if (entity != null && ThirdPartyAuthType.WECHAT_OPEN == entity.getAuthType() && entity.getOrgId().equals(orgId)) {
            WechatOpenConfig config = authWechatOpenService.getConfig(entity);
            if (config == null || !config.isAuthorized()) {
                throw new BadRequestException("公众号未授权");
            }
            return syncTemplate(entity, config);
        }
        return List.of();
    }

    @Transactional
    public List<ThirdPartyTemplate> syncTemplate(ThirdPartyAuth entity, WechatOpenConfig config) {
        List<ThirdPartyTemplate> oldList = repository.findByThirdpartyAuthId(entity.getId());
        List<WxMpTemplate> newList = weChatOpenService.getTemplateList(config.getAppId());
        if (newList == null) {
            // 查询失败，直接结束同步
            return oldList;
        }
        ListHelper.diff(oldList, ThirdPartyTemplate::getOpenId,
                newList, WxMpTemplate::getTemplateId,
                (o, n) -> false,
                add -> {
                    List<ThirdPartyTemplate> list = add.stream().map(i -> build(entity.getOrgId(), entity.getId(), i)).collect(Collectors.toList());
                    repository.saveAll(list);
                }, update -> {
                    update.forEach((wxMpTemplate, thirdPartyTemplate) -> {
                        updateParameters(thirdPartyTemplate, wxMpTemplate);
                        repository.save(thirdPartyTemplate);
                    });
                }, delete -> {
                    repository.deleteAll(delete);
                });
        config.setLastSyncTimeTemplate(new Date());
        authWechatOpenService.updateConfig(entity, config);
        return null;
    }

    private ThirdPartyTemplate build(Long orgId, Long thirdpartyAuthId, WxMpTemplate template) {
        List<Map<String, Object>> parameters = TemplateParser.parseWechatTemplate(template.getContent());
        return new ThirdPartyTemplate(orgId, thirdpartyAuthId, template, parameters);
    }

    private void updateParameters(ThirdPartyTemplate thirdPartyTemplate, WxMpTemplate template) {
        List<Map<String, Object>> parameters = TemplateParser.parseWechatTemplate(template.getContent());
        thirdPartyTemplate.setParameters(parameters);
    }

    public List<ThirdPartyTemplate> deleteWechatTemplate(Long orgId, Long configId) {
        ThirdPartyAuth entity = thirdPartyAuthService.get(configId);
        if (entity != null && ThirdPartyAuthType.WECHAT_OPEN == entity.getAuthType() && entity.getOrgId().equals(orgId)) {
            List<ThirdPartyTemplate> list = repository.findByThirdpartyAuthId(entity.getId());
            if (CollectionUtils.isNotEmpty(list)) {
                repository.deleteAll(list);
            }
        }
        return List.of();
    }

    public List<ThirdPartyTemplate> getAllWechatTemplate() {
        return repository.findByConnectorType(ConnectorType.PLATFORM);
    }

    public List<ThirdPartyTemplate> getByIds(List<Long> ids) {
        return repository.findAllById(ids);
    }
}
