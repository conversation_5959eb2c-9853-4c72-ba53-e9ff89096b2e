<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.hanyi</groupId>
        <artifactId>parent-ctm</artifactId>
        <version>${revision}.${sha1}-${changelist}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>ctm-data-core</artifactId>
    <!--  要修改版本号，直接改最外层pom的revision  -->
    <version>${revision}.${sha1}-${changelist}</version>
    <name>ctm-data-core</name>
    <description>ctm data core project</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <spring-boot.repackage.skip>true</spring-boot.repackage.skip>
        <java.version>${java.version}</java.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>cn.hanyi</groupId>
            <artifactId>ctm-common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hanyi</groupId>
            <artifactId>cem-core</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hanyi</groupId>
            <artifactId>ctm-customer-core</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hanyi</groupId>
            <artifactId>survey-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun.datahub</groupId>
            <artifactId>aliyun-sdk-datahub</artifactId>
            <version>2.19.3-public</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.aliyun.datahub</groupId>
            <artifactId>datahub-client-library</artifactId>
            <version>1.1.12-public</version>
        </dependency>
        <dependency>
            <groupId>org.apache.pulsar</groupId>
            <artifactId>pulsar-client</artifactId>
            <version>2.7.2</version>
        </dependency>
        <dependency>
            <groupId>com.jayway.jsonpath</groupId>
            <artifactId>json-path</artifactId>
            <version>2.8.0</version>
        </dependency>
    </dependencies>
</project>
