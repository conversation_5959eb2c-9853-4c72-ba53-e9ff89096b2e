<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>cn.hanyi</groupId>
    <artifactId>parent-ctm</artifactId>
    <version>${revision}.${sha1}-${changelist}</version>
    <packaging>pom</packaging>

    <name>parent</name>
    <url>http://maven.apache.org</url>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.4.5</version>
    </parent>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>14</java.version>
        <maven.compiler.plugin.version>3.5.1</maven.compiler.plugin.version>
        <maven.compiler.source>14</maven.compiler.source>
        <maven.compiler.target>14</maven.compiler.target>
        <spring.version>2.4.5</spring.version>
        <hanyi.version>1.0-SNAPSHOT</hanyi.version>
        <auto-service.version>1.0-rc2</auto-service.version>
        <openapi.version>1.5.2</openapi.version>
        <pg.version>42.2.5</pg.version>
        <lombok.version>1.18.30</lombok.version>
        <cqrcb-compat.version>1.0.1-SNAPSHOT</cqrcb-compat.version>
        <!--   ctm 的版本号  https://maven.apache.org/maven-ci-friendly.html  -->
        <revision>1.11.7</revision>
        <sha1>2</sha1>
        <changelist>RELEASE</changelist>
        <core.version>1.12.0.7-RELEASE</core.version>
        <auth.version>1.11.7.0-RELEASE</auth.version>
        <survey.version>1.11.7.3-RELEASE</survey.version>
        <worker.version>1.11.7.0-RELEASE</worker.version>
        <current.version>${revision}.${sha1}-${changelist}</current.version>
    </properties>

    <modules>
        <module>ctm</module>
        <module>ctm-common-core</module>
        <module>ctm-customer-core</module>
        <module>ctm-event-core</module>
        <module>ctm-journey-core</module>
        <module>ctm-task</module>
        <module>ctm-trigger</module>
        <module>ctm-data-core</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.befun</groupId>
                <artifactId>befun-core</artifactId>
                <version>${core.version}</version>
            </dependency>
            <dependency>
                <groupId>org.befun.extension</groupId>
                <artifactId>befun-x-pack</artifactId>
                <version>${core.version}</version>
            </dependency>
            <dependency>
                <groupId>org.befun.task</groupId>
                <artifactId>befun-task</artifactId>
                <version>${core.version}</version>
            </dependency>
            <dependency>
                <groupId>org.befun.auth</groupId>
                <artifactId>befun-auth-core</artifactId>
                <version>${auth.version}</version>
            </dependency>
            <dependency>
                <groupId>org.befun.auth</groupId>
                <artifactId>befun-auth-pay</artifactId>
                <version>${auth.version}</version>
            </dependency>
            <dependency>
                <groupId>org.befun.auth</groupId>
                <artifactId>befun-auth-trigger</artifactId>
                <version>${auth.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hanyi</groupId>
                <artifactId>survey-core</artifactId>
                <version>${survey.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hanyi</groupId>
                <artifactId>cem-core</artifactId>
                <version>${worker.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hanyi</groupId>
                <artifactId>ctm-common-core</artifactId>
                <version>${current.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hanyi</groupId>
                <artifactId>ctm-customer-core</artifactId>
                <version>${current.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hanyi</groupId>
                <artifactId>ctm-data-core</artifactId>
                <version>${current.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hanyi</groupId>
                <artifactId>ctm-event-core</artifactId>
                <version>${current.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hanyi</groupId>
                <artifactId>ctm-journey-core</artifactId>
                <version>${current.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hanyi</groupId>
                <artifactId>ctm-task</artifactId>
                <version>${current.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hanyi</groupId>
                <artifactId>ctm-trigger</artifactId>
                <version>${current.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hanyi</groupId>
                <artifactId>ctm-data-core</artifactId>
                <version>${current.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>


    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <executable>true</executable>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.5.1</version>
                <configuration>
                    <source>14</source>
                    <target>14</target>
                    <encoding>UTF-8</encoding>
                    <generatedSourcesDirectory>${project.build.directory}/generated-sources</generatedSourcesDirectory>
                    <annotationProcessorPaths>
                        <annotationProcessorPath>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </annotationProcessorPath>
                        <annotationProcessorPath>
                            <groupId>org.befun</groupId>
                            <artifactId>befun-core</artifactId>
                            <version>${core.version}</version>
                        </annotationProcessorPath>
                    </annotationProcessorPaths>
                    <annotationProcessors>
                        <annotationProcessor>lombok.launch.AnnotationProcessorHider$AnnotationProcessor
                        </annotationProcessor>
                        <annotationProcessor>org.befun.core.annotation.AnnotationProcessor
                        </annotationProcessor>
                    </annotationProcessors>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.1.0</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>verify</phase>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
