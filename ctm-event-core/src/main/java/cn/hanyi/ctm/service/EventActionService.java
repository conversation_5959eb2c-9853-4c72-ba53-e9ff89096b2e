package cn.hanyi.ctm.service;

import cn.hanyi.ctm.constant.EventActionStatusType;
import cn.hanyi.ctm.constant.EventActionType;
import cn.hanyi.ctm.constant.EventStatusType;
import cn.hanyi.ctm.dto.TemplateInfoDto;
import cn.hanyi.ctm.dto.event.*;
import cn.hanyi.ctm.dto.task.DataWarningRemarkDto;
import cn.hanyi.ctm.entity.*;
import cn.hanyi.ctm.properties.NotifyProperties;
import cn.hanyi.ctm.repository.EventActionRepository;
import cn.hanyi.ctm.repository.EventRepository;
import cn.hanyi.ctm.service.stat.CacheStatEventService;
import cn.hanyi.ctm.workertrigger.ICtmEventTrigger;
import cn.hanyi.ctm.workertrigger.ICtmTaskTrigger;
import cn.hanyi.ctm.workertrigger.dto.CustomerSendSmsDto;
import cn.hanyi.ctm.workertrigger.dto.CustomerSendWechatDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.befun.auth.constant.NotificationType;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.UserService;
import org.befun.auth.service.UserTaskService;
import org.befun.auth.service.auth.AuthWechatOpenService;
import org.befun.auth.service.auth.config.WechatOpenConfig;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.CrudService;
import org.befun.core.utils.DateHelper;
import org.befun.core.utils.RegHelper;
import org.befun.extension.property.SmsTemplateProperty;
import org.befun.extension.sms.ISmsAccountService;
import org.befun.task.constant.TaskStatus;
import org.befun.task.entity.TaskProgress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static org.befun.auth.constant.UserTaskType.event_notify_customer;


@Slf4j
@Service
public class EventActionService {

    @Autowired
    private EventService eventService;
    @Autowired
    private EventActionRepository eventActionRepository;
    @Autowired
    private NotifyProperties notifyProperties;
    @Autowired
    private UserService userService;
    @Autowired
    private CacheStatEventService cacheStatEventService;
    @Autowired
    private ISmsAccountService smsAccountService;
    @Autowired
    private UserTaskService userTaskService;
    @Autowired
    private ICtmTaskTrigger ctmTaskTrigger;
    @Autowired
    private ICtmEventTrigger ctmEventTrigger;
    @Autowired
    private CustomerService customerService;
    @Autowired
    private CustomerMessageService customerMessageService;
    @Autowired
    private ThirdPartyTemplateService thirdPartyTemplateService;
    @Autowired
    private AuthWechatOpenService authWechatOpenService;
    @Autowired
    private CrudService crudService;
    @Autowired
    private EventRepository eventRepository;


    /**
     * 1 保存 action 操作
     * 2 更新 event 最后操作人和时间
     */
    public EventAction addAction2(SimpleUser user, EventActionType type, String content, EventActionStatusType status, Event event) {
        EventAction action = new EventAction();
        action.setOrgId(event.getOrgId());
        action.setEvent(event);
        action.setSurveyId(event.getSurveyId());
        action.setContent(content);
        action.setActionType(type);
        action.setActionUserId(user.getId());
        action.setActionUsername(user.getTruename());
        action.setActionStatus(status);
        eventActionRepository.save(action);
        event.setLastActionUsername(user.getTruename());
        event.setLastActionTime(new Date());
        eventService.save(event);
        return action;
    }

    /**
     * 1 保存 action 操作
     * 2 更新 event 最后操作人和时间
     */
    public boolean addAction(SimpleUser user, EventActionType type, String content, EventActionStatusType status, Event event) {
        addAction2(user, type, content, status, event);
        return true;
    }


    /**
     * 1 保存 action 操作
     * 2 更新 event 最后操作人和时间
     */
    public boolean addAction(SimpleUser user, EventActionType type, String content, EventActionStatusType status, Event event, List<String> attachments) {
        EventAction action = new EventAction();
        action.setOrgId(event.getOrgId());
        action.setEvent(event);
        action.setSurveyId(event.getSurveyId());
        action.setContent(content);
        action.setActionType(type);
        action.setActionUserId(user.getId());
        action.setActionUsername(user.getTruename());
        action.setActionStatus(status);
        action.setAttachments(attachments);
        eventActionRepository.save(action);
        event.setLastActionUsername(user.getTruename());
        event.setLastActionTime(new Date());
        eventService.save(event);
        return true;
    }


    public boolean addActionSingle(SimpleUser user, EventActionType type, String content, EventActionStatusType status, Event event) {
        EventAction action = new EventAction();
        action.setOrgId(event.getOrgId());
        action.setEvent(event);
        action.setSurveyId(event.getSurveyId());
        action.setContent(content);
        action.setActionType(type);
        action.setActionUserId(user.getId());
        action.setActionUsername(user.getTruename());
        action.setActionStatus(status);
        eventActionRepository.save(action);
        return true;
    }

    public boolean addActionTargetUsers(SimpleUser user, EventActionType type, String content, EventActionStatusType status, Event event, List<SimpleUser> targetUsers) {
        EventAction action = new EventAction();
        action.setOrgId(event.getOrgId());
        action.setEvent(event);
        action.setSurveyId(event.getSurveyId());
        action.setContent(content);
        action.setActionType(type);
        action.setActionUserId(user.getId());
        action.setActionUsername(user.getTruename());
        action.setActionStatus(status);
        action.setTargetUserIds(targetUsers.stream().map(SimpleUser::getId).collect(Collectors.toList()));
        eventActionRepository.save(action);
        event.setLastActionUsername(user.getTruename());
        event.setLastActionTime(new Date());
        eventService.save(event);
        return true;
    }


    @Transactional(rollbackFor = Exception.class)
    public boolean actionSms(long eventId) {
        Event event = eventService.require(eventId);
        Long customerId = event.getCustomerId();
        if (customerId == null || customerId <= 0) {
            return false;
        }
        Customer customer = customerService.get(customerId);
        if (customer == null || !RegHelper.isMobile(customer.getMobile())) {
            return false;
        }
        Long orgId = TenantContext.requireCurrentTenant();
        Long userId = TenantContext.requireCurrentUserId();
        try {
            SmsTemplateProperty smsTemplate = thirdPartyTemplateService.getPlatformSmsTemplate(notifyProperties.getCustomer());
            TemplateInfoDto templateInfo = TemplateInfoDto.createSms(0L, smsTemplate.getId(), smsTemplate.getName(), smsTemplate.getSignature(), smsTemplate.getRealSignature(), smsTemplate.getContent());
            customerMessageService.checkSmsCost(orgId, templateInfo, 1);
            TaskProgress taskProgress = userTaskService.createTask(orgId, userId, event_notify_customer, 1, null);
            smsAccountService.buildSmsTask(orgId, userId, taskProgress.getId(), templateInfo.fullSmsTemplate(), planCost -> {
                Map<String, Object> placeholderParams = this.customerMessageService.buildNativeParams(null, "", customer, null);
                String content = this.customerMessageService.buildSmsContent(templateInfo.getSmsContent(), placeholderParams);
                CustomerSendSmsDto dto = CustomerSendSmsDto.fromEvent(orgId, userId, taskProgress.getId(),
                        eventId, null, content, customer.getMobile(), smsTemplate.getId(), notifyProperties.getCustomer(),
                        smsTemplate.getSignature(), smsTemplate.getRealSignature(), null, null);
                ctmTaskTrigger.customerSendSms(dto);
                smsAccountService.addSmsTask(orgId, taskProgress.getId(), 1L, planCost);
            });
            userTaskService.updateTaskStatus(taskProgress.getId(), TaskStatus.RUNNING);
        } catch (Exception e) {
            log.error("发送给客户短信通知失败，客户id：{}，事件id：{}", customerId, event.getId(), e);
        }
        return true;
    }

    /**
     * worker 回调写入事件处理记录
     */
    public void addSendSmsAction(Long orgId, Long userId, Long eventId, String content, boolean success) {
        Event event = eventService.get(eventId);
        if (event != null) {
            EventActionStatusType status = success ? EventActionStatusType.SUCCESS : EventActionStatusType.FAILED;
            userService.getSimple(userId).ifPresent(user -> addAction(user, EventActionType.ACTION_TYPE_MESSAGE, content, status, event));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean actionWechat(long eventId, EventActionWechatRequestDto action) {
        Event event = eventService.require(eventId);
        Long customerId = event.getCustomerId();
        Customer customer;
        if (customerId == null || customerId < 0 || (customer = customerService.get(customerId)) == null) {
            return false;
        }
        ThirdPartyCustomer thirdPartyCustomer = customerMessageService.getWechatCustomer(customer);
        if (thirdPartyCustomer == null || StringUtils.isEmpty(thirdPartyCustomer.getOpenId())) {
            return false;
        }
        Long orgId = TenantContext.getCurrentTenant();
        Long userId = TenantContext.getCurrentUserId();
        try {
            WechatOpenConfig config = authWechatOpenService.getConfig(thirdPartyCustomer.getThirdpartyAuthId());
            if (config != null) {
                String openId = thirdPartyCustomer.getOpenId();
                String appId = config.getAppId();
                TaskProgress taskProgress = userTaskService.createTask(orgId, userId, event_notify_customer, 1, null);
                ctmTaskTrigger.customerSendWechat(new CustomerSendWechatDto(orgId, userId, taskProgress.getId(), eventId, config.getConfigId(), appId, openId, action.getContent()));
                return true;
            }
        } catch (Exception e) {
            log.error("发送给客户微信通知失败，客户id：{}，事件id：{}", customerId, event.getId(), e);
        }
        return false;
    }

    /**
     * worker 回调写入事件处理记录
     */
    public void addSendWechatAction(Long orgId, Long userId, Long eventId, String content, boolean success) {
        Event event = eventService.get(eventId);
        if (event != null) {
            EventActionStatusType status = success ? EventActionStatusType.SUCCESS : EventActionStatusType.FAILED;
            userService.getSimple(userId).ifPresent(user -> addAction(user, EventActionType.ACTION_TYPE_WECHAT, content, status, event));
        }
    }

//    /**
//     * 构建微信客服消息
//     */
//    private TextPushRequestDto buildWechatMessage(Long orgId, Long customerId, String content) {
//        TextPushRequestDto dto = new TextPushRequestDto();
//        dto.setIsDetail(true);
//        dto.setOrgId(orgId);
//        dto.setCustomerIds(Lists.newArrayList(customerId));
//        Map<String, Object> contentMap = new HashMap<>();
//        contentMap.put("content", content);
//        dto.setContent(contentMap);
//        dto.setConnectorType(ConnectorType.PLATFORM);
//        dto.setProviderType(ConnectorProviderType.WECHATOPEN);
//        dto.setParameters(new HashMap<>());
//        return dto;
//    }

    @Transactional(rollbackFor = Exception.class)
    public boolean actionCooperation(long eventId, EventActionCooperationRequestDto action) {
        // # 给目标用户发送一条协作行动站内信
        // 1 解析所有目标用户
        // 2 找到当前用户
        // 3 给每个目标用户发送站内信和微信|邮件|短信通知
        Event event = eventService.require(eventId);
        Long orgId = TenantContext.getCurrentTenant();
        Long userId = TenantContext.getCurrentUserId();
        List<Long> targetUserIds = Optional.ofNullable(action.getTargetUserIds())
                .map(i -> Arrays.stream(i.split(",")))
                .map(s -> s.filter(NumberUtils::isDigits).map(Long::parseLong).collect(Collectors.toList())).orElse(null);
        if (CollectionUtils.isEmpty(targetUserIds)) {
            throw new BadRequestException("参数targetUserIds错误");
        }
        List<SimpleUser> targetUsers = userService.getSimpleByIds(new HashSet<>(targetUserIds));
        SimpleUser current = userService.getSimple(userId).orElse(null);
        if (current == null) {
            throw new AccessDeniedException("登录已过期");
        }
        if (CollectionUtils.isNotEmpty(targetUsers)) {
            eventCooperation(orgId, event, targetUsers, action.getTypes(), action.getContent(), current);
        }

        String cooperationUserName = targetUsers.stream().map(SimpleUser::getTruename).collect(Collectors.joining("、"));
        String content = String.format("协作给%s", cooperationUserName);
        if (!StringUtils.isEmpty(action.getContent())) {
            content += "：" + action.getContent();
        }
        return addActionTargetUsers(current, EventActionType.ACTION_TYPE_COOPERATION, content, EventActionStatusType.SUCCESS, event, targetUsers);
    }


    /**
     * 发送事件协作通知
     */
    public void eventCooperation(Long orgId, Event event, List<SimpleUser> targetUsers, List<NotificationType> notificationTypes, String content, SimpleUser current) {
        ctmEventTrigger.eventCooperate(
                orgId,
                event.getId(),
                current.getId(),
                targetUsers.stream().map(SimpleUser::getId).collect(Collectors.toSet()),
                content,
                notificationTypes.stream().map(NotificationType::name).distinct().collect(Collectors.toList()));
//        eventActionService.asyncNotifyCooperation(orgId, event, targetUsers, notificationTypes, content, current);
    }


//    public void asyncNotifyCooperation(Long orgId, Event event, List<SimpleUser> users, List<NotificationType> notificationTypes, String content, SimpleUser current) {
//        InboxMessage inboxMessage = eventNotifyService.buildActionCooperationInboxMessage(orgId, current, event);
//        NotificationType[] types = eventNotifyService.parseNotificationType(notificationTypes);
//        String notifyTemplate = notifyProperties.getCooperation();
//        String app = notifyProperties.getApp();
//        Map<String, Object> notifyParams = eventNotifyService.buildNotifyParam(current, event, Map.of("content", content));
//        users.forEach(i -> {
//            eventNotifyService.addInboxMessage(i.getId(), inboxMessage);
//            notifyParams.put("targetTruename", i.getTruename() == null ? "" : i.getTruename());
//            eventNotifyService.sendToUser(i.getId(), app, types, notifyTemplate, notifyParams);
//            // user add event permission
//            eventNotifyService.grantEventPermission(event, i.getId());
//        });
//    }

    @Transactional(rollbackFor = Exception.class)
    public boolean actionWebReply(long eventId, EventActionWebReplyDto action) {
        Event event = eventService.require(eventId);
        Long userId = TenantContext.getCurrentUserId();
        return userService.getSimple(userId)
                .map(user -> addAction(user, EventActionType.ACTION_TYPE_WEB_REPLY, action.getContent(), EventActionStatusType.SUCCESS, event))
                .orElse(false);
    }

    public EventActionWebReplyDto getWebReplyAction(Long responseId) {
        EventActionWebReplyDto replyDto = new EventActionWebReplyDto();
        Optional<Event> event = eventRepository.findOneByResponseId(responseId);
        event.ifPresent(e -> {
            Optional<EventAction> action = eventActionRepository.findFirstByEventIdAndActionTypeOrderByCreateTimeDesc(e.getId(), EventActionType.ACTION_TYPE_WEB_REPLY);
            action.ifPresent(eventAction -> {
                if (eventAction.getActionStatus() == EventActionStatusType.SUCCESS) {
                    replyDto.setContent(eventAction.getContent());
                }
                eventAction.setActionStatus(EventActionStatusType.READ);
                eventActionRepository.save(eventAction);
            });
        });
        return replyDto;
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean actionRemark(long eventId, EventActionRemarkRequestDto action) {
        Event event = eventService.require(eventId);
        Long userId = TenantContext.getCurrentUserId();
        return userService.getSimple(userId)
                .map(user -> {
                    return addAction(user, EventActionType.ACTION_TYPE_NOTE, action.getContent(), EventActionStatusType.SUCCESS, event, action.getAttachments());
                })
                .orElse(false);
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean actionClose(long eventId, EventActionCloseRequestDto action) {
        Event event = eventService.require(eventId);
        Long userId = TenantContext.getCurrentUserId();
        Long orgId = TenantContext.getCurrentTenant();
        SimpleUser current = userService.getSimple(userId).orElse(null);
        if (current == null) {
            return false;
        }

        String content = "关闭了此行动";
        String userName = current.getTruename();
        // 带参数的关闭事件需要修改action_username 和content
        if (action != null) {
            content = action.getComment() == null ? content : action.getComment();
            userName = action.getUserName() == null ? userName : action.getUserName();
        }

        // 需要使用参数的userName
        SimpleUser currentUser = new SimpleUser(
                current.getId(),
                userName,
                current.getAvatar(),
                current.getIsAdmin(),
                current.getMobile(),
                current.getEmail()
        );

        eventClose(orgId, event, currentUser);
        event.setStatus(EventStatusType.SUCCESS);
        // 事件关闭了，需要重置该事件的统计数据
        if (event.getWarnings() != null && event.getCreateTime() != null) {
            List<Long> ruleIds = event.getWarnings().stream().filter(Objects::nonNull).map(EventWarningDto::getRuleId).filter(Objects::nonNull).collect(Collectors.toList());
            cacheStatEventService.clearByEventStatus(ruleIds, DateHelper.toLocalDate(event.getCreateTime()));
        }

        EventAction eventAction = addAction2(currentUser, EventActionType.ACTION_TYPE_CLOSE, content, EventActionStatusType.SUCCESS, event);

        ctmEventTrigger.eventChangeStatus(
                TenantContext.requireCurrentTenant(),
                TenantContext.requireCurrentUserId(),
                event.getId(),
                eventAction.getId(),
                event.getStatus().name()
        );
        return true;
    }

    /**
     * 发送事件关闭通知
     */
    public void eventClose(Long orgId, Event event, SimpleUser current) {
        ctmEventTrigger.eventClose(orgId, event.getId(), current.getId());
//        eventActionService.asyncNotifyClose(orgId, event, current);
    }

    /**
     * 拉取协作人员
     */
    public List<SimpleUser> cooperationUsers(Long eventId) {
        List<SimpleUser> userDtos = new ArrayList<>();
        Set<Long> existUserIds = new HashSet<>();
        eventActionRepository.findByEventIdAndActionType(eventId, EventActionType.ACTION_TYPE_COOPERATION).forEach(action -> {
            action.getTargetUserIds().forEach(userId -> {
                if (existUserIds.contains(userId)) {
                    return;
                }
                existUserIds.add(userId);
                userService.getSimple(userId).ifPresent(userDtos::add);
            });
        });

        return userDtos;
    }

//    public void asyncNotifyClose(Long orgId, Event event, SimpleUser current) {
//        // 所有的预警规则
//        List<EventWarningDto> warnings = event.getWarnings();
//        if (CollectionUtils.isNotEmpty(warnings)) {
//            Map<Long, SimpleUser> notifyUsers = new HashMap<>();
//            warnings.forEach(warning -> {
//                if (warning.getRuleId() != null && warning.getRuleId() > 0) {
//                    EventMonitorRules rule = eventMonitorRulesRepository.findById(warning.getRuleId()).orElse(null);
//                    if (rule != null) {
//                        // 预警规则的通知规则列表
//                        List<EventReceiverDto> receivers = rule.getReceiver();
//                        if (CollectionUtils.isNotEmpty(receivers)) {
//                            receivers.forEach(receiver -> {
//                                // 通知规则的匹配用户
//                                List<SimpleUser> users = eventNotifyService.getNotifyUsers(event.getOrgId(), event.getDepartmentId(), Set.of(receiver.getRoleId()));
//                                if (CollectionUtils.isNotEmpty(users)) {
//                                    users.forEach(user -> {
//                                        notifyUsers.put(user.getId(), user);
//                                    });
//                                }
//                            });
//                        }
//                    }
//                }
//            });
//            if (notifyUsers.size() > 0) {
//                InboxMessage inboxMessage = eventNotifyService.buildActionCloseInboxMessage(orgId, current, event);
//                NotificationType[] types = eventNotifyService.parseNotificationType(Lists.newArrayList(NotificationType.WECHAT, NotificationType.WECHAT_WORK, NotificationType.EMAIL));
//                String notifyTemplate = notifyProperties.getClose();
//                String app = notifyProperties.getApp();
//                Map<String, Object> notifyParams = eventNotifyService.buildNotifyParam(current, event, null);
//                notifyUsers.values().forEach(user -> {
//                    // inbox
//                    eventNotifyService.addInboxMessage(user.getId(), inboxMessage);
//                    // send wx/email to user
//                    notifyParams.put("targetTruename", user.getTruename() == null ? "" : user.getTruename());
//                    eventNotifyService.sendToUser(user.getId(), app, types, notifyTemplate, notifyParams);
//                });
//            }
//        }
//    }

    public Map<Long, List<EventAction>> getRemarkByEventsGroupByEvent(Collection<Long> eventIds) {
        Map<Long, List<EventAction>> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(eventIds)) {
            List<EventAction> list = eventActionRepository.findByEventIdInAndActionType(eventIds, EventActionType.ACTION_TYPE_NOTE);
            if (CollectionUtils.isNotEmpty(list)) {
                list.forEach(i -> {
                    Long eventId = i.getEvent().getId();
                    map.computeIfAbsent(eventId, k -> new ArrayList<>()).add(i);
                });
            }
        }
        return map;
    }

    public List<EventActionDto> getAllActionsByEvent(long eventId) {
        List<EventAction> list = eventActionRepository.findByEventIdOrderByCreateTimeDesc(eventId);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(list)) {
            return crudService.mapToDto(list, EventActionDto.class);
        }
        return new ArrayList<>();
    }

    public EventAction get(Long actionId) {
        if (actionId != null && actionId > 0) {
            return eventActionRepository.findById(actionId).orElse(null);
        }
        return null;
    }

    public List<DataWarningRemarkDto> getRemarksByEventId(Long eventId) {
        List<DataWarningRemarkDto> list = new ArrayList<>();
        if (eventId != null && eventId > 0) {
            List<EventAction> remarks = eventActionRepository.findByEventIdAndActionType(eventId, EventActionType.ACTION_TYPE_NOTE);
            if (CollectionUtils.isNotEmpty(remarks)) {
                remarks.forEach(i -> {
                    list.add(0, new DataWarningRemarkDto(i.getContent(), i.getAttachments()));
                });
            }
        }
        return list;
    }

}
