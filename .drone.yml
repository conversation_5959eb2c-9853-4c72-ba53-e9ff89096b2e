kind: pipeline
type: docker
name: default

platform:
  os: linux
  arch: amd64

clone:
  depth: 1

steps:
  - name: branch-build
    image: registry-vpc.cn-shenzhen.aliyuncs.com/hanyi-public/maven:3.6.3-openjdk-14
    pull: if-not-exists
    volumes:
      - name: m2-global-settings
        path: /root/.m2/settings.xml
      - name: ctm
        path: /tmp/repository
    environment:
      MAVEN_CONFIG: /drone/src/.m2
    commands:
      - cat /root/.m2/settings.xml
      - mvn clean install -U -s /root/.m2/settings.xml -Dtest=cn.hanyi.ctm.StartUpTest -DfailIfNoTests=false -Dmaven.repo.local=/tmp/repository
      - echo -n "${DRONE_BRANCH}" > .tags
    when:
      ref:
        - refs/heads/**

  - name: tag-build
    image: registry-vpc.cn-shenzhen.aliyuncs.com/hanyi-public/maven:3.6.3-openjdk-14
    pull: if-not-exists
    volumes:
      - name: m2-global-settings
        path: /root/.m2/settings.xml
      - name: ctm
        path: /tmp/repository
    environment:
      MAVEN_CONFIG: /drone/src/.m2
    commands:
      - cat /root/.m2/settings.xml
      - mvn clean install -s /root/.m2/settings.xml -Dtest=cn.hanyi.ctm.StartUpTest -DfailIfNoTests=false -Dmaven.repo.local=/tmp/repository
      - echo -n "${DRONE_TAG}" > .tags
    when:
      ref:
      - refs/tags/**


  - name: publish
    image: plugins/docker
    pull: if-not-exists
    volumes:
      - name: docker-auths
        path: /root/.docker/config.json
    settings:
      repo: registry-vpc.cn-shenzhen.aliyuncs.com/surveyplus/ctm
      registry: registry-vpc.cn-shenzhen.aliyuncs.com

  - name: ssh commands
    image: appleboy/drone-ssh
    volumes:
      - name: ssh_rsa
        path: /root/.ssh/id_rsa
    settings:
      key_path: /root/.ssh/id_rsa
      host: **********
      username: root
      script:
        - source /etc/profile
        - ifconfig ens192
        - kubectl -n survey-dev rollout restart deployment ctm
    when:
      branch:
      - develop

  - name: test trigger
    image: plugins/webhook
    pull: if-not-exists
    settings:
      mecthod: POST
      urls:
        - https://cs.console.aliyun.com/hook/trigger?token=*****************************************************************************************************************************************************************************************************************************************************************************************************
    when:
      branch:
      - test

  - name: notify
    image: fifsky/drone-wechat-work
    pull: if-not-exists
    settings:
      url: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=0accac60-9d20-446f-8e8a-4f758da26b57
      msgtype: markdown
      content: |
        {{if eq .Status "success" }}
        #### 🎉 ${DRONE_REPO} 构建成功
        > Commit: [${DRONE_COMMIT_MESSAGE}](${DRONE_COMMIT_LINK})
        > Author: ${DRONE_COMMIT_AUTHOR}
        > Branch: ${DRONE_BRANCH}
        > Tag: ${DRONE_TAG}
        > [点击查看](${DRONE_BUILD_LINK})
        {{else}}
        #### ❌ ${DRONE_REPO} 构建失败
        > Commit: [${DRONE_COMMIT_MESSAGE}](${DRONE_COMMIT_LINK})
        > Author: ${DRONE_COMMIT_AUTHOR}
        > Branch: ${DRONE_BRANCH}
        > Tag: ${DRONE_TAG}
        > 请立即修复!!!
        > [点击查看](${DRONE_BUILD_LINK})
        {{end}}
    when:
      status:
        - failure
        - success

  - name: 上线发布
    image: fifsky/drone-wechat-work
    pull: if-not-exists
    settings:
      url: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=1dece01d-6c33-40fc-bd8e-ada57f5fa538
      msgtype: markdown
      content: |
        #### 🎉 ${DRONE_REPO} 构建成功
        > Tag: ${DRONE_TAG}
        > [选择镜像](https://cs.console.aliyun.com/?spm=5176.2020520152.console-base.dcsk.5d6416ddmXRoEq#/k8s/cluster/cad922aece6ae44e7b879bfdd9ac0bfdd/v2/workload/deployment/update/survey/ctm?from=detail&type=deployment&clusterType=ManagedKubernetes&profile=Default&state=running&ns=survey&region=cn-shenzhen)
    when:
      status:
        - success
      ref:
        - refs/tags/**



volumes:
  - name: cache
    host:
      path: /data/drone/cache
  - name: m2-global-settings
    host:
      path: /data/drone/settings.xml
  - name: docker-auths
    host:
      path: /root/.docker/config.json
  - name: m2-global-settings
    host:
      path: /data/drone/settings.xml
  - name: ssh_rsa
    host:
      path: /root/.ssh/id_rsa
  - name: ctm
    host:
      path: /data/drone/cache/ctm

trigger:
  ref:
 #   - refs/heads/master
    - refs/heads/test
    - refs/heads/develop
    - refs/tags/**

image_pull_secrets:
- dockerconfig


